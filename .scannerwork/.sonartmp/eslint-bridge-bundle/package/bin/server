#!/usr/bin/env node

/**
 * This script expects following arguments
 *
 * port - port number on which server should listen
 * host - host address on which server should listen
 * workDir - working directory from SonarQube API
 * shouldUseTypeScriptParserForJS - whether TypeScript parser should be used for JS code (default true, can be set to false in case of perf issues)
 * sonarlint - when running in SonarLint (used to not compute metrics, highlighting, etc)
 * bundles - ; or : delimited paths to additional rule bundles
 */

const server = require('../lib/server');
const path = require('path');
const context = require('../lib/helpers');

const port = process.argv[2];
const host = process.argv[3];
const workDir = process.argv[4];
const shouldUseTypeScriptParserForJS = process.argv[5] === 'true';
const sonarlint = process.argv[6] === 'true';

let bundles = [];
if (process.argv[7]) {
  bundles = process.argv[7].split(path.delimiter);
}

context.setContext({ workDir, shouldUseTypeScriptParserForJS, sonarlint, bundles });
server.start(port, host);
