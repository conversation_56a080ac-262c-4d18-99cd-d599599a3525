"use strict";
/*
 * SonarQube JavaScript Plugin
 * Copyright (C) 2011-2023 SonarSource SA
 * mailto:info AT sonarsource DOT com
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */
// https://sonarsource.github.io/rspec/#/rspec/S1438/javascript
Object.defineProperty(exports, "__esModule", { value: true });
exports.decorateSemi = void 0;
const helpers_1 = require("./helpers");
function decorateSemi(rule) {
    return (0, helpers_1.interceptReport)(rule, fixLocation);
}
exports.decorateSemi = decorateSemi;
function fixLocation(context, reportDescriptor) {
    var _a;
    const report = reportDescriptor;
    if (((_a = report.loc) === null || _a === void 0 ? void 0 : _a.start) && report.messageId === 'missingSemi') {
        report.loc = report.loc.start;
    }
    context.report(reportDescriptor);
}
//# sourceMappingURL=semi-decorator.js.map