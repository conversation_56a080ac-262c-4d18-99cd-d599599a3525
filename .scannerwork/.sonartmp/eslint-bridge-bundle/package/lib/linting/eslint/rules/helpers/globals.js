"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalsByLibraries = void 0;
/*
 * SonarQube JavaScript Plugin
 * Copyright (C) 2011-2023 SonarSource SA
 * mailto:info AT sonarsource DOT com
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */
// copied from javascript-frontend/src/main/resources/org/sonar/javascript/tree/symbols/globals.json ,
// which should be deleted once the frontend migration is over
exports.globalsByLibraries = {
    builtin: [
        'Array',
        'ArrayBuffer',
        'Boolean',
        'DataView',
        'Date',
        'Error',
        'EvalError',
        'Float32Array',
        'Float64Array',
        'Function',
        'Infinity',
        'Int16Array',
        'Int32Array',
        'Int8Array',
        'JSON',
        'Map',
        'Math',
        'NaN',
        'Number',
        'Object',
        'Promise',
        'Proxy',
        'RangeError',
        'ReferenceError',
        'Reflect',
        'RegExp',
        'Set',
        'String',
        'Symbol',
        'SyntaxError',
        'TypeError',
        'URIError',
        'Uint16Array',
        'Uint32Array',
        'Uint8Array',
        'Uint8ClampedArray',
        'WeakMap',
        'WeakSet',
        'constructor',
        'decodeURI',
        'decodeURIComponent',
        'encodeURI',
        'encodeURIComponent',
        'escape',
        'eval',
        'hasOwnProperty',
        'isFinite',
        'isNaN',
        'isPrototypeOf',
        'parseFloat',
        'parseInt',
        'propertyIsEnumerable',
        'toLocaleString',
        'toString',
        'unescape',
        'valueOf',
    ],
    // https://developer.mozilla.org/en-US/docs/Web/API/Window
    // https://developer.mozilla.org/en-US/docs/Web/API/Document_Object_Model
    browser: [
        'addEventListener',
        'alert',
        'AnalyserNode',
        'Animation',
        'AnimationEffectReadOnly',
        'AnimationEffectTiming',
        'AnimationEffectTimingReadOnly',
        'AnimationEvent',
        'AnimationPlaybackEvent',
        'AnimationTimeline',
        'applicationCache',
        'ApplicationCache',
        'ApplicationCacheErrorEvent',
        'atob',
        'Attr',
        'Audio',
        'AudioBuffer',
        'AudioBufferSourceNode',
        'AudioContext',
        'AudioDestinationNode',
        'AudioListener',
        'AudioNode',
        'AudioParam',
        'AudioProcessingEvent',
        'AutocompleteErrorEvent',
        'BarProp',
        'BatteryManager',
        'BeforeUnloadEvent',
        'BiquadFilterNode',
        'Blob',
        'blur',
        'btoa',
        'Cache',
        'caches',
        'CacheStorage',
        'cancelAnimationFrame',
        'CanvasGradient',
        'CanvasPattern',
        'CanvasRenderingContext2D',
        'CDATASection',
        'ChannelMergerNode',
        'ChannelSplitterNode',
        'CharacterData',
        'clearInterval',
        'clearTimeout',
        'clientInformation',
        'ClientRect',
        'ClientRectList',
        'ClipboardEvent',
        'close',
        'closed',
        'CloseEvent',
        'Comment',
        'CompositionEvent',
        'confirm',
        'console',
        'ConvolverNode',
        'Credential',
        'CredentialsContainer',
        'crypto',
        'Crypto',
        'CryptoKey',
        'CSS',
        'CSSAnimation',
        'CSSFontFaceRule',
        'CSSImportRule',
        'CSSKeyframeRule',
        'CSSKeyframesRule',
        'CSSMediaRule',
        'CSSPageRule',
        'CSSRule',
        'CSSRuleList',
        'CSSStyleDeclaration',
        'CSSStyleRule',
        'CSSStyleSheet',
        'CSSSupportsRule',
        'CSSTransition',
        'CSSUnknownRule',
        'CSSViewportRule',
        'customElements',
        'CustomEvent',
        'DataTransfer',
        'DataTransferItem',
        'DataTransferItemList',
        'Debug',
        'defaultStatus',
        'defaultstatus',
        'DelayNode',
        'DeviceMotionEvent',
        'DeviceOrientationEvent',
        'devicePixelRatio',
        'dispatchEvent',
        'document',
        'Document',
        'DocumentFragment',
        'DocumentTimeline',
        'DocumentType',
        'DOMError',
        'DOMException',
        'DOMImplementation',
        'DOMParser',
        'DOMSettableTokenList',
        'DOMStringList',
        'DOMStringMap',
        'DOMTokenList',
        'DragEvent',
        'DynamicsCompressorNode',
        'Element',
        'ElementTimeControl',
        'ErrorEvent',
        'event',
        'Event',
        'EventSource',
        'EventTarget',
        'external',
        'FederatedCredential',
        'fetch',
        'File',
        'FileError',
        'FileList',
        'FileReader',
        'find',
        'focus',
        'FocusEvent',
        'FontFace',
        'FormData',
        'frameElement',
        'frames',
        'GainNode',
        'Gamepad',
        'GamepadButton',
        'GamepadEvent',
        'getComputedStyle',
        'getSelection',
        'HashChangeEvent',
        'Headers',
        'history',
        'History',
        'HTMLAllCollection',
        'HTMLAnchorElement',
        'HTMLAppletElement',
        'HTMLAreaElement',
        'HTMLAudioElement',
        'HTMLBaseElement',
        'HTMLBlockquoteElement',
        'HTMLBodyElement',
        'HTMLBRElement',
        'HTMLButtonElement',
        'HTMLCanvasElement',
        'HTMLCollection',
        'HTMLContentElement',
        'HTMLDataListElement',
        'HTMLDetailsElement',
        'HTMLDialogElement',
        'HTMLDirectoryElement',
        'HTMLDivElement',
        'HTMLDListElement',
        'HTMLDocument',
        'HTMLElement',
        'HTMLEmbedElement',
        'HTMLFieldSetElement',
        'HTMLFontElement',
        'HTMLFormControlsCollection',
        'HTMLFormElement',
        'HTMLFrameElement',
        'HTMLFrameSetElement',
        'HTMLHeadElement',
        'HTMLHeadingElement',
        'HTMLHRElement',
        'HTMLHtmlElement',
        'HTMLIFrameElement',
        'HTMLImageElement',
        'HTMLInputElement',
        'HTMLIsIndexElement',
        'HTMLKeygenElement',
        'HTMLLabelElement',
        'HTMLLayerElement',
        'HTMLLegendElement',
        'HTMLLIElement',
        'HTMLLinkElement',
        'HTMLMapElement',
        'HTMLMarqueeElement',
        'HTMLMediaElement',
        'HTMLMenuElement',
        'HTMLMetaElement',
        'HTMLMeterElement',
        'HTMLModElement',
        'HTMLObjectElement',
        'HTMLOListElement',
        'HTMLOptGroupElement',
        'HTMLOptionElement',
        'HTMLOptionsCollection',
        'HTMLOutputElement',
        'HTMLParagraphElement',
        'HTMLParamElement',
        'HTMLPictureElement',
        'HTMLPreElement',
        'HTMLProgressElement',
        'HTMLQuoteElement',
        'HTMLScriptElement',
        'HTMLSelectElement',
        'HTMLShadowElement',
        'HTMLSourceElement',
        'HTMLSpanElement',
        'HTMLStyleElement',
        'HTMLTableCaptionElement',
        'HTMLTableCellElement',
        'HTMLTableColElement',
        'HTMLTableElement',
        'HTMLTableRowElement',
        'HTMLTableSectionElement',
        'HTMLTemplateElement',
        'HTMLTextAreaElement',
        'HTMLTitleElement',
        'HTMLTrackElement',
        'HTMLUListElement',
        'HTMLUnknownElement',
        'HTMLVideoElement',
        'IDBCursor',
        'IDBCursorWithValue',
        'IDBDatabase',
        'IDBEnvironment',
        'IDBFactory',
        'IDBIndex',
        'IDBKeyRange',
        'IDBObjectStore',
        'IDBOpenDBRequest',
        'IDBRequest',
        'IDBTransaction',
        'IDBVersionChangeEvent',
        'Image',
        'ImageBitmap',
        'ImageData',
        'indexedDB',
        'innerHeight',
        'innerWidth',
        'InputEvent',
        'InputMethodContext',
        'IntersectionObserver',
        'IntersectionObserverEntry',
        'Intl',
        'KeyboardEvent',
        'KeyframeEffect',
        'KeyframeEffectReadOnly',
        'length',
        'localStorage',
        'location',
        'Location',
        'locationbar',
        'matchMedia',
        'MediaElementAudioSourceNode',
        'MediaEncryptedEvent',
        'MediaError',
        'MediaKeyError',
        'MediaKeyEvent',
        'MediaKeyMessageEvent',
        'MediaKeys',
        'MediaKeySession',
        'MediaKeyStatusMap',
        'MediaKeySystemAccess',
        'MediaList',
        'MediaQueryList',
        'MediaQueryListEvent',
        'MediaSource',
        'MediaStream',
        'MediaStreamAudioDestinationNode',
        'MediaStreamAudioSourceNode',
        'MediaStreamEvent',
        'MediaStreamTrack',
        'menubar',
        'MessageChannel',
        'MessageEvent',
        'MessagePort',
        'MIDIAccess',
        'MIDIConnectionEvent',
        'MIDIInput',
        'MIDIInputMap',
        'MIDIMessageEvent',
        'MIDIOutput',
        'MIDIOutputMap',
        'MIDIPort',
        'MimeType',
        'MimeTypeArray',
        'MouseEvent',
        'moveBy',
        'moveTo',
        'MutationEvent',
        'MutationObserver',
        'MutationRecord',
        'name',
        'NamedNodeMap',
        'navigator',
        'Navigator',
        'Node',
        'NodeFilter',
        'NodeIterator',
        'NodeList',
        'Notification',
        'OfflineAudioCompletionEvent',
        'OfflineAudioContext',
        'offscreenBuffering',
        'onbeforeunload',
        'onblur',
        'onerror',
        'onfocus',
        'onload',
        'onresize',
        'onunload',
        'open',
        'openDatabase',
        'opener',
        'opera',
        'Option',
        'OscillatorNode',
        'outerHeight',
        'outerWidth',
        'PageTransitionEvent',
        'pageXOffset',
        'pageYOffset',
        'parent',
        'PasswordCredential',
        'Path2D',
        'performance',
        'Performance',
        'PerformanceEntry',
        'PerformanceMark',
        'PerformanceMeasure',
        'PerformanceNavigation',
        'PerformanceResourceTiming',
        'PerformanceTiming',
        'PeriodicWave',
        'Permissions',
        'PermissionStatus',
        'personalbar',
        'Plugin',
        'PluginArray',
        'PopStateEvent',
        'postMessage',
        'print',
        'ProcessingInstruction',
        'ProgressEvent',
        'PromiseRejectionEvent',
        'prompt',
        'PushManager',
        'PushSubscription',
        'RadioNodeList',
        'Range',
        'ReadableByteStream',
        'ReadableStream',
        'removeEventListener',
        'Request',
        'requestAnimationFrame',
        'requestIdleCallback',
        'resizeBy',
        'resizeTo',
        'Response',
        'RTCIceCandidate',
        'RTCSessionDescription',
        'RTCPeerConnection',
        'screen',
        'Screen',
        'screenLeft',
        'ScreenOrientation',
        'screenTop',
        'screenX',
        'screenY',
        'ScriptProcessorNode',
        'scroll',
        'scrollbars',
        'scrollBy',
        'scrollTo',
        'scrollX',
        'scrollY',
        'SecurityPolicyViolationEvent',
        'Selection',
        'self',
        'ServiceWorker',
        'ServiceWorkerContainer',
        'ServiceWorkerRegistration',
        'sessionStorage',
        'setInterval',
        'setTimeout',
        'ShadowRoot',
        'SharedKeyframeList',
        'SharedWorker',
        'showModalDialog',
        'SiteBoundCredential',
        'speechSynthesis',
        'SpeechSynthesisEvent',
        'SpeechSynthesisUtterance',
        'status',
        'statusbar',
        'stop',
        'Storage',
        'StorageEvent',
        'styleMedia',
        'StyleSheet',
        'StyleSheetList',
        'SubtleCrypto',
        'SVGAElement',
        'SVGAltGlyphDefElement',
        'SVGAltGlyphElement',
        'SVGAltGlyphItemElement',
        'SVGAngle',
        'SVGAnimateColorElement',
        'SVGAnimatedAngle',
        'SVGAnimatedBoolean',
        'SVGAnimatedEnumeration',
        'SVGAnimatedInteger',
        'SVGAnimatedLength',
        'SVGAnimatedLengthList',
        'SVGAnimatedNumber',
        'SVGAnimatedNumberList',
        'SVGAnimatedPathData',
        'SVGAnimatedPoints',
        'SVGAnimatedPreserveAspectRatio',
        'SVGAnimatedRect',
        'SVGAnimatedString',
        'SVGAnimatedTransformList',
        'SVGAnimateElement',
        'SVGAnimateMotionElement',
        'SVGAnimateTransformElement',
        'SVGAnimationElement',
        'SVGCircleElement',
        'SVGClipPathElement',
        'SVGColor',
        'SVGColorProfileElement',
        'SVGColorProfileRule',
        'SVGComponentTransferFunctionElement',
        'SVGCSSRule',
        'SVGCursorElement',
        'SVGDefsElement',
        'SVGDescElement',
        'SVGDiscardElement',
        'SVGDocument',
        'SVGElement',
        'SVGElementInstance',
        'SVGElementInstanceList',
        'SVGEllipseElement',
        'SVGEvent',
        'SVGExternalResourcesRequired',
        'SVGFEBlendElement',
        'SVGFEColorMatrixElement',
        'SVGFEComponentTransferElement',
        'SVGFECompositeElement',
        'SVGFEConvolveMatrixElement',
        'SVGFEDiffuseLightingElement',
        'SVGFEDisplacementMapElement',
        'SVGFEDistantLightElement',
        'SVGFEDropShadowElement',
        'SVGFEFloodElement',
        'SVGFEFuncAElement',
        'SVGFEFuncBElement',
        'SVGFEFuncGElement',
        'SVGFEFuncRElement',
        'SVGFEGaussianBlurElement',
        'SVGFEImageElement',
        'SVGFEMergeElement',
        'SVGFEMergeNodeElement',
        'SVGFEMorphologyElement',
        'SVGFEOffsetElement',
        'SVGFEPointLightElement',
        'SVGFESpecularLightingElement',
        'SVGFESpotLightElement',
        'SVGFETileElement',
        'SVGFETurbulenceElement',
        'SVGFilterElement',
        'SVGFilterPrimitiveStandardAttributes',
        'SVGFitToViewBox',
        'SVGFontElement',
        'SVGFontFaceElement',
        'SVGFontFaceFormatElement',
        'SVGFontFaceNameElement',
        'SVGFontFaceSrcElement',
        'SVGFontFaceUriElement',
        'SVGForeignObjectElement',
        'SVGGElement',
        'SVGGeometryElement',
        'SVGGlyphElement',
        'SVGGlyphRefElement',
        'SVGGradientElement',
        'SVGGraphicsElement',
        'SVGHKernElement',
        'SVGICCColor',
        'SVGImageElement',
        'SVGLangSpace',
        'SVGLength',
        'SVGLengthList',
        'SVGLinearGradientElement',
        'SVGLineElement',
        'SVGLocatable',
        'SVGMarkerElement',
        'SVGMaskElement',
        'SVGMatrix',
        'SVGMetadataElement',
        'SVGMissingGlyphElement',
        'SVGMPathElement',
        'SVGNumber',
        'SVGNumberList',
        'SVGPaint',
        'SVGPathElement',
        'SVGPathSeg',
        'SVGPathSegArcAbs',
        'SVGPathSegArcRel',
        'SVGPathSegClosePath',
        'SVGPathSegCurvetoCubicAbs',
        'SVGPathSegCurvetoCubicRel',
        'SVGPathSegCurvetoCubicSmoothAbs',
        'SVGPathSegCurvetoCubicSmoothRel',
        'SVGPathSegCurvetoQuadraticAbs',
        'SVGPathSegCurvetoQuadraticRel',
        'SVGPathSegCurvetoQuadraticSmoothAbs',
        'SVGPathSegCurvetoQuadraticSmoothRel',
        'SVGPathSegLinetoAbs',
        'SVGPathSegLinetoHorizontalAbs',
        'SVGPathSegLinetoHorizontalRel',
        'SVGPathSegLinetoRel',
        'SVGPathSegLinetoVerticalAbs',
        'SVGPathSegLinetoVerticalRel',
        'SVGPathSegList',
        'SVGPathSegMovetoAbs',
        'SVGPathSegMovetoRel',
        'SVGPatternElement',
        'SVGPoint',
        'SVGPointList',
        'SVGPolygonElement',
        'SVGPolylineElement',
        'SVGPreserveAspectRatio',
        'SVGRadialGradientElement',
        'SVGRect',
        'SVGRectElement',
        'SVGRenderingIntent',
        'SVGScriptElement',
        'SVGSetElement',
        'SVGStopElement',
        'SVGStringList',
        'SVGStylable',
        'SVGStyleElement',
        'SVGSVGElement',
        'SVGSwitchElement',
        'SVGSymbolElement',
        'SVGTests',
        'SVGTextContentElement',
        'SVGTextElement',
        'SVGTextPathElement',
        'SVGTextPositioningElement',
        'SVGTitleElement',
        'SVGTransform',
        'SVGTransformable',
        'SVGTransformList',
        'SVGTRefElement',
        'SVGTSpanElement',
        'SVGUnitTypes',
        'SVGURIReference',
        'SVGUseElement',
        'SVGViewElement',
        'SVGViewSpec',
        'SVGVKernElement',
        'SVGZoomAndPan',
        'SVGZoomEvent',
        'Text',
        'TextDecoder',
        'TextEncoder',
        'TextEvent',
        'TextMetrics',
        'TextTrack',
        'TextTrackCue',
        'TextTrackCueList',
        'TextTrackList',
        'TimeEvent',
        'TimeRanges',
        'toolbar',
        'top',
        'Touch',
        'TouchEvent',
        'TouchList',
        'TrackEvent',
        'TransitionEvent',
        'TreeWalker',
        'UIEvent',
        'URL',
        'URLSearchParams',
        'ValidityState',
        'VTTCue',
        'WaveShaperNode',
        'WebGLActiveInfo',
        'WebGLBuffer',
        'WebGLContextEvent',
        'WebGLFramebuffer',
        'WebGLProgram',
        'WebGLRenderbuffer',
        'WebGLRenderingContext',
        'WebGLShader',
        'WebGLShaderPrecisionFormat',
        'WebGLTexture',
        'WebGLUniformLocation',
        'WebSocket',
        'WheelEvent',
        'window',
        'Window',
        'Worker',
        'XDomainRequest',
        'XMLDocument',
        'XMLHttpRequest',
        'XMLHttpRequestEventTarget',
        'XMLHttpRequestProgressEvent',
        'XMLHttpRequestUpload',
        'XMLSerializer',
        'XPathEvaluator',
        'XPathException',
        'XPathExpression',
        'XPathNamespace',
        'XPathNSResolver',
        'XPathResult',
        'XSLTProcessor',
    ],
    worker: [
        'applicationCache',
        'atob',
        'Blob',
        'BroadcastChannel',
        'btoa',
        'Cache',
        'caches',
        'clearInterval',
        'clearTimeout',
        'close',
        'console',
        'fetch',
        'FileReaderSync',
        'FormData',
        'Headers',
        'IDBCursor',
        'IDBCursorWithValue',
        'IDBDatabase',
        'IDBFactory',
        'IDBIndex',
        'IDBKeyRange',
        'IDBObjectStore',
        'IDBOpenDBRequest',
        'IDBRequest',
        'IDBTransaction',
        'IDBVersionChangeEvent',
        'ImageData',
        'importScripts',
        'indexedDB',
        'location',
        'MessageChannel',
        'MessagePort',
        'name',
        'navigator',
        'Notification',
        'onclose',
        'onconnect',
        'onerror',
        'onlanguagechange',
        'onmessage',
        'onoffline',
        'ononline',
        'onrejectionhandled',
        'onunhandledrejection',
        'performance',
        'Performance',
        'PerformanceEntry',
        'PerformanceMark',
        'PerformanceMeasure',
        'PerformanceNavigation',
        'PerformanceResourceTiming',
        'PerformanceTiming',
        'postMessage',
        'Promise',
        'Request',
        'Response',
        'self',
        'ServiceWorkerRegistration',
        'setInterval',
        'setTimeout',
        'TextDecoder',
        'TextEncoder',
        'URL',
        'URLSearchParams',
        'WebSocket',
        'Worker',
        'XMLHttpRequest',
    ],
    // https://nodejs.org/api/globals.html
    node: [
        '__dirname',
        '__filename',
        'Buffer',
        'clearImmediate',
        'clearInterval',
        'clearTimeout',
        'console',
        'exports',
        'global',
        'module',
        'process',
        'require',
        'setImmediate',
        'setInterval',
        'setTimeout',
    ],
    commonjs: ['exports', 'module', 'require', 'global'],
    amd: ['define', 'require'],
    // https://mochajs.org/
    mocha: [
        'after',
        'afterEach',
        'before',
        'beforeEach',
        'context',
        'describe',
        'it',
        'mocha',
        'run',
        'setup',
        'specify',
        'suite',
        'suiteSetup',
        'suiteTeardown',
        'teardown',
        'test',
        'xcontext',
        'xdescribe',
        'xit',
        'xspecify',
    ],
    // https://jasmine.github.io/2.0/introduction.html
    jasmine: [
        'afterAll',
        'afterEach',
        'beforeAll',
        'beforeEach',
        'describe',
        'expect',
        'fail',
        'fdescribe',
        'fit',
        'it',
        'jasmine',
        'pending',
        'runs',
        'spyOn',
        'waits',
        'waitsFor',
        'xdescribe',
        'xit',
    ],
    jest: [
        'afterAll',
        'afterEach',
        'beforeAll',
        'beforeEach',
        'check',
        'describe',
        'expect',
        'gen',
        'it',
        'fit',
        'jest',
        'pit',
        'require',
        'test',
        'xdescribe',
        'xit',
        'xtest',
    ],
    qunit: [
        'asyncTest',
        'deepEqual',
        'equal',
        'expect',
        'module',
        'notDeepEqual',
        'notEqual',
        'notOk',
        'notPropEqual',
        'notStrictEqual',
        'ok',
        'propEqual',
        'QUnit',
        'raises',
        'start',
        'stop',
        'strictEqual',
        'test',
        'throws',
    ],
    phantomjs: ['console', 'exports', 'phantom', 'require', 'WebPage'],
    couch: [
        'emit',
        'exports',
        'getRow',
        'log',
        'module',
        'provides',
        'require',
        'respond',
        'send',
        'start',
        'sum',
    ],
    rhino: [
        'defineClass',
        'deserialize',
        'gc',
        'help',
        'importClass',
        'importPackage',
        'java',
        'load',
        'loadClass',
        'Packages',
        'print',
        'quit',
        'readFile',
        'readUrl',
        'runCommand',
        'seal',
        'serialize',
        'spawn',
        'sync',
        'toint32',
        'version',
    ],
    nashorn: [
        '__DIR__',
        '__FILE__',
        '__LINE__',
        'com',
        'edu',
        'exit',
        'Java',
        'java',
        'javafx',
        'JavaImporter',
        'javax',
        'JSAdapter',
        'load',
        'loadWithNewGlobal',
        'org',
        'Packages',
        'print',
        'quit',
    ],
    wsh: [
        'ActiveXObject',
        'Enumerator',
        'GetObject',
        'ScriptEngine',
        'ScriptEngineBuildVersion',
        'ScriptEngineMajorVersion',
        'ScriptEngineMinorVersion',
        'VBArray',
        'WScript',
        'WSH',
        'XDomainRequest',
    ],
    jquery: ['$', 'jQuery'],
    yui: ['Y', 'YUI', 'YUI_config'],
    shelljs: [
        'cat',
        'cd',
        'chmod',
        'config',
        'cp',
        'dirs',
        'echo',
        'env',
        'error',
        'exec',
        'exit',
        'find',
        'grep',
        'ls',
        'ln',
        'mkdir',
        'mv',
        'popd',
        'pushd',
        'pwd',
        'rm',
        'sed',
        'set',
        'target',
        'tempdir',
        'test',
        'touch',
        'which',
    ],
    prototypejs: [
        '$',
        '$$',
        '$A',
        '$break',
        '$continue',
        '$F',
        '$H',
        '$R',
        '$w',
        'Abstract',
        'Ajax',
        'Autocompleter',
        'Builder',
        'Class',
        'Control',
        'Draggable',
        'Draggables',
        'Droppables',
        'Effect',
        'Element',
        'Enumerable',
        'Event',
        'Field',
        'Form',
        'Hash',
        'Insertion',
        'ObjectRange',
        'PeriodicalExecuter',
        'Position',
        'Prototype',
        'Scriptaculous',
        'Selector',
        'Sortable',
        'SortableObserver',
        'Sound',
        'Template',
        'Toggle',
        'Try',
    ],
    meteor: [
        '$',
        '_',
        'Accounts',
        'AccountsClient',
        'AccountsServer',
        'AccountsCommon',
        'App',
        'Assets',
        'Blaze',
        'check',
        'Cordova',
        'DDP',
        'DDPServer',
        'DDPRateLimiter',
        'Deps',
        'EJSON',
        'Email',
        'HTTP',
        'Log',
        'Match',
        'Meteor',
        'Mongo',
        'MongoInternals',
        'Npm',
        'Package',
        'Plugin',
        'process',
        'Random',
        'ReactiveDict',
        'ReactiveVar',
        'Router',
        'ServiceConfiguration',
        'Session',
        'share',
        'Spacebars',
        'Template',
        'Tinytest',
        'Tracker',
        'UI',
        'Utils',
        'WebApp',
        'WebAppInternals',
    ],
    mongo: [
        '_isWindows',
        '_rand',
        'BulkWriteResult',
        'cat',
        'cd',
        'connect',
        'db',
        'getHostName',
        'getMemInfo',
        'hostname',
        'ISODate',
        'listFiles',
        'load',
        'ls',
        'md5sumFile',
        'mkdir',
        'Mongo',
        'NumberInt',
        'NumberLong',
        'ObjectId',
        'PlanCache',
        'print',
        'printjson',
        'pwd',
        'quit',
        'removeFile',
        'rs',
        'sh',
        'UUID',
        'version',
        'WriteResult',
    ],
    applescript: [
        '$',
        'Application',
        'Automation',
        'console',
        'delay',
        'Library',
        'ObjC',
        'ObjectSpecifier',
        'Path',
        'Progress',
        'Ref',
    ],
    serviceworker: [
        'caches',
        'Cache',
        'CacheStorage',
        'Client',
        'clients',
        'Clients',
        'ExtendableEvent',
        'ExtendableMessageEvent',
        'FetchEvent',
        'importScripts',
        'registration',
        'self',
        'ServiceWorker',
        'ServiceWorkerContainer',
        'ServiceWorkerGlobalScope',
        'ServiceWorkerMessageEvent',
        'ServiceWorkerRegistration',
        'skipWaiting',
        'WindowClient',
    ],
    atomtest: [
        'advanceClock',
        'fakeClearInterval',
        'fakeClearTimeout',
        'fakeSetInterval',
        'fakeSetTimeout',
        'resetTimeouts',
        'waitsForPromise',
    ],
    // https://guides.emberjs.com/v1.10.0/testing/test-helpers/
    embertest: [
        'andThen',
        'click',
        'currentPath',
        'currentRouteName',
        'currentURL',
        'fillIn',
        'find',
        'findWithAssert',
        'keyEvent',
        'pauseTest',
        'triggerEvent',
        'visit',
    ],
    protractor: ['$', '$$', 'browser', 'By', 'by', 'DartObject', 'element', 'protractor'],
    'shared-node-browser': ['clearInterval', 'clearTimeout', 'console', 'setInterval', 'setTimeout'],
    webextensions: ['browser', 'chrome', 'opr'],
    greasemonkey: [
        'GM_addStyle',
        'GM_deleteValue',
        'GM_getResourceText',
        'GM_getResourceURL',
        'GM_getValue',
        'GM_info',
        'GM_listValues',
        'GM_log',
        'GM_openInTab',
        'GM_registerMenuCommand',
        'GM_setClipboard',
        'GM_setValue',
        'GM_xmlhttpRequest',
        'unsafeWindow',
    ],
    flow: [
        'boolean',
        'number',
        'string',
        'null',
        'void',
        'mixed',
        'any',
        'empty',
        'Array',
        'Class',
        '$Call',
        '$TupleMap',
        '$ObjMap',
        '$ElementType',
        '$PropertyType',
        '$Rest',
        '$Diff',
        '$Exact',
        '$ReadOnly',
        '$ReadOnlyArray',
        '$Values',
        '$Keys',
        '$SuperType',
        '$Subtype',
        'RegExp$flags',
        'stream$Writable',
        'stream$Readable',
        'tty$WriteStream',
        'tty$ReadStream',
    ],
};
//# sourceMappingURL=globals.js.map