"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.parseAwsFromYaml = void 0;
/*
 * SonarQube JavaScript Plugin
 * Copyright (C) 2011-2023 SonarSource SA
 * mailto:info AT sonarsource DOT com
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 3 of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program; if not, write to the Free Software Foundation,
 * Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA.
 */
const parser_1 = require("parsing/yaml/parser");
const parsingContexts_1 = require("./parsingContexts");
/**
 * Extracts from a YAML file all the embedded JavaScript code snippets either
 * in AWS Lambda Functions or AWS Serverless Functions.
 */
exports.parseAwsFromYaml = parser_1.parseYaml.bind(null, [
    parsingContexts_1.lambdaParsingContext,
    parsingContexts_1.serverlessParsingContext,
]);
//# sourceMappingURL=parser.js.map