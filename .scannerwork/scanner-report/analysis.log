Plugins:
  - Chinese Pack 9.9 (l10nzh)
Bundled analyzers:
  - Python Code Quality and Security 3.24.1.11916 (python)
  - Go Code Quality and Security 1.11.0.3905 (go)
  - JaCoCo 1.3.0.1538 (jacoco)
  - Kotlin Code Quality and Security 2.12.0.1956 (kotlin)
  - IaC Code Quality and Security 1.11.0.2847 (iac)
  - JavaScript/TypeScript/CSS Code Quality and Security 9.13.0.20537 (javascript)
  - Ruby Code Quality and Security 1.11.0.3905 (ruby)
  - Scala Code Quality and Security 1.11.0.3905 (sonarscala)
  - C# Code Quality and Security 8.51.0.59060 (csharp)
  - Java Code Quality and Security 7.16.0.30901 (java)
  - HTML Code Quality and Security 3.7.1.3306 (web)
  - Flex Code Quality and Security 2.8.0.3166 (flex)
  - XML Code Quality and Security 2.7.0.3820 (xml)
  - PHP Code Quality and Security 3.27.1.9352 (php)
  - Text Code Quality and Security 2.0.2.1090 (text)
  - VB.NET Code Quality and Security 8.51.0.59060 (vbnet)
  - Configuration detection fot Code Quality and Security 1.2.0.267 (config)
Global server settings:
  - sonar.core.id=147B411E-AZgHudUHOB8daYXS0_6m
  - sonar.core.startTime=2025-07-14T08:43:57+0000
  - sonar.forceAuthentication=true
  - sonar.plugins.risk.consent=ACCEPTED
Project server settings:
Project scanner properties:
  - sonar.exclusions=**/*.log,**/*.tmp,**/node_modules/**,**/target/**,**/build/**,**/.git/**,**/venv/**,**/__pycache__/**,**/dist/*,**/txt/*
  - sonar.host.url=http://sonarqube.ingress.local:30088
  - sonar.login=******
  - sonar.projectBaseDir=D:\code\cninfo_process
  - sonar.projectKey=cninfo-spider
  - sonar.scanner.apiBaseUrl=http://sonarqube.ingress.local:30088/api/v2
  - sonar.scanner.app=ScannerCLI
  - sonar.scanner.appVersion=7.1.0.4889
  - sonar.scanner.arch=amd64
  - sonar.scanner.bootstrapStartTime=1752487378881
  - sonar.scanner.home=D:\soft\sonar-scanner-7.1.0.4889-windows-x64\bin\..
  - sonar.scanner.os=windows
  - sonar.scanner.wasEngineCacheHit=true
  - sonar.sources=.
  - sonar.working.directory=D:\code\cninfo_process\.scannerwork
  - sonar.ws.timeout=60
