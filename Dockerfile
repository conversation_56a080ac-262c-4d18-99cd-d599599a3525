# 多阶段构建 - 前端构建阶段
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ ./
RUN npm run build

# 后端运行阶段
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制后端代码
COPY backend/ ./backend/
COPY docker-deploy/ ./docker-deploy/

# 安装Python依赖
RUN pip install --no-cache-dir -r backend/requirements.txt

# 复制前端构建结果
COPY --from=frontend-builder /app/frontend/out ./frontend/out

# 创建必要目录
RUN mkdir -p data/database txt pdf exports results

# 设置环境变量
ENV FLASK_ENV=production
ENV PYTHONPATH=/app/backend

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["python", "backend/app.py"]
