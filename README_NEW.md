# 巨潮资讯网年报爬虫工具 - 重构版

## 项目概述

这是一个基于Next.js + Flask的现代化年报分析工具，支持从巨潮资讯网爬取年报数据，进行关键词分析和AI智能分析。

## 技术栈

### 前端
- **Next.js 14** - React框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 现代化样式
- **Axios** - HTTP客户端
- **React Hot Toast** - 消息提示

### 后端
- **Flask** - Python Web框架
- **SQLite** - 轻量级数据库
- **Pandas** - 数据处理
- **OpenAI** - AI分析
- **PDFPlumber** - PDF文本提取

## 项目结构

```
cninfo_process/
├── frontend/                 # Next.js前端
│   ├── app/                 # App Router
│   ├── components/          # React组件
│   ├── lib/                 # 工具库
│   └── package.json
├── backend/                 # Flask后端
│   ├── config/              # 配置模块
│   ├── models/              # 数据模型
│   ├── services/            # 业务逻辑
│   ├── routes/              # 路由模块
│   ├── utils/               # 工具函数
│   ├── app.py               # 主应用
│   └── requirements.txt
├── docker-deploy/           # 原版本(保留)
└── README.md
```

## 功能特性

### 🔍 年报分析
- 在线爬取巨潮资讯网年报数据
- 本地TXT文件导入和分析
- 关键词统计和上下文提取
- 关联方协同创新分析

### 🤖 AI智能分析
- 基于OpenAI的智能分析
- 流式响应实时显示
- 自定义分析提示词
- Markdown格式输出

### 📊 数据可视化
- 现代化Web界面
- 实时进度显示
- 数据筛选和排序
- 分页显示

### 📁 数据管理
- SQLite数据库存储
- Excel/CSV导出
- 重复数据清理
- 批量文件处理

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.8+

### 1. 克隆项目
```bash
git clone <repository-url>
cd cninfo_process
```

### 2. 启动后端
```bash
cd backend
pip install -r requirements.txt
python app.py
```

### 3. 启动前端
```bash
cd frontend
npm install
npm run dev
```

### 4. 访问应用
打开浏览器访问 http://localhost:3000

## 配置说明

### 环境变量
在backend目录创建`.env`文件：

```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 数据库配置
DATABASE_PATH=cninfo_reports.db

# 文件路径配置
TXT_DIR=txt
PDF_DIR=pdf
EXPORTS_DIR=exports
```

### 前端配置
前端会自动代理API请求到后端，无需额外配置。

## 使用指南

### 1. 在线年报分析
1. 选择"年报分析"标签
2. 输入股票代码（每行一个）
3. 输入分析关键词
4. 设置时间范围
5. 点击"开始分析"

### 2. 本地关键词分析
1. 选择"关键词分析"标签
2. 确保已导入TXT文件
3. 输入股票代码和关键词
4. 点击"开始分析"

### 3. AI智能分析
1. 选择"AI分析"标签
2. 配置OpenAI参数
3. 输入分析要求
4. 查看流式分析结果

### 4. 数据导入
1. 选择"数据导入"标签
2. 指定TXT文件目录
3. 点击导入按钮
4. 查看导入结果

## API接口

### 分析接口
- `POST /api/start_analysis` - 开始在线分析
- `POST /api/keyword_analysis` - 本地关键词分析
- `GET /api/task_status/<task_id>` - 获取任务状态
- `GET /api/analysis_results/<task_id>` - 获取分析结果

### 数据接口
- `GET /api/companies` - 获取公司列表
- `POST /api/import_txt` - 导入TXT文件
- `POST /api/search_reports` - 搜索年报

### AI接口
- `POST /api/ai_analysis` - AI分析
- `POST /api/ai_analysis_stream` - 流式AI分析
- `POST /api/test_openai` - 测试OpenAI连接

### 导出接口
- `GET /api/export_results/<task_id>` - 导出分析结果
- `POST /api/export_filtered_results` - 导出筛选结果

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t cninfo-app .

# 运行容器
docker run -p 3000:3000 -p 5000:5000 cninfo-app
```

### 生产环境
1. 设置环境变量 `FLASK_ENV=production`
2. 使用Gunicorn运行后端
3. 使用Nginx代理前端

## 开发说明

### 后端架构
- **config/** - 配置管理，支持多环境
- **models/** - 数据模型和任务管理
- **services/** - 业务逻辑层，包含爬虫、分析、AI、导出服务
- **routes/** - 路由层，按功能模块分组
- **utils/** - 工具函数，响应格式化和验证

### 前端架构
- **app/** - Next.js App Router页面
- **components/** - 可复用React组件
- **lib/** - API客户端和工具函数

### 代码规范
- 后端使用Python类型提示
- 前端使用TypeScript严格模式
- 统一的错误处理和响应格式
- 模块化设计，低耦合高内聚

## 常见问题

### Q: 如何配置OpenAI API？
A: 在backend目录创建.env文件，设置OPENAI_API_KEY等参数。

### Q: 如何导入本地TXT文件？
A: 将TXT文件放在backend/txt目录，然后使用数据导入功能。

### Q: 如何查看分析进度？
A: 启动分析任务后，界面会显示实时进度条和状态信息。

### Q: 支持哪些导出格式？
A: 支持Excel(.xlsx)和CSV(.csv)格式导出。

## 更新日志

### v2.0.0 (2024-01-XX)
- 🎉 完全重构为Next.js + Flask架构
- ✨ 新增现代化Web界面
- 🚀 优化性能和用户体验
- 🔧 模块化代码结构
- 📱 响应式设计支持

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
