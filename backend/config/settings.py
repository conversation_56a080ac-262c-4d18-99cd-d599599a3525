"""
应用配置模块
"""
import os
from typing import Optional

class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'cninfo_spider_secret_key_2024')
    
    # 数据库配置
    DATABASE_PATH = os.getenv('DATABASE_PATH', 'cninfo_reports.db')
    
    # OpenAI配置
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
    OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
    
    # 文件路径配置
    TXT_DIR = os.getenv('TXT_DIR', 'txt')
    PDF_DIR = os.getenv('PDF_DIR', 'pdf')
    EXPORTS_DIR = os.getenv('EXPORTS_DIR', 'exports')
    RESULTS_DIR = os.getenv('RESULTS_DIR', 'results')
    
    # 爬虫配置
    REQUEST_TIMEOUT = int(os.getenv('REQUEST_TIMEOUT', '10'))
    REQUEST_DELAY = float(os.getenv('REQUEST_DELAY', '1.0'))
    MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))
    
    # 分页配置
    DEFAULT_PAGE_SIZE = int(os.getenv('DEFAULT_PAGE_SIZE', '20'))
    MAX_PAGE_SIZE = int(os.getenv('MAX_PAGE_SIZE', '100'))
    
    @classmethod
    def get_database_path(cls) -> str:
        """自动检测环境并返回合适的数据库路径"""
        # 检查是否在Docker容器中
        if os.path.exists('/.dockerenv'):
            # Docker环境
            db_path = 'data/database/cninfo_reports.db'
            print("🐳 检测到Docker环境")
        else:
            # 本地环境，检查多个可能的路径
            possible_paths = [
                cls.DATABASE_PATH,  # 环境变量指定的路径
                'cninfo_reports.db',  # 当前目录
                'database/cninfo_reports.db',  # database子目录
                '../cninfo_reports.db',  # 上级目录
                'data/database/cninfo_reports.db',  # Docker挂载路径
            ]

            db_path = None
            print(f"🔍 正在检查数据库路径...")
            for path in possible_paths:
                print(f"  检查: {path} -> {'存在' if os.path.exists(path) else '不存在'}")
                if os.path.exists(path):
                    db_path = path
                    print(f"💻 本地环境，找到数据库: {path}")
                    break

            if not db_path:
                # 如果都没找到，使用默认路径
                db_path = cls.DATABASE_PATH
                print(f"💻 本地环境，使用默认路径: {db_path}")

        return db_path
    
    @classmethod
    def ensure_directories(cls):
        """确保必要目录存在"""
        directories = [
            'data/database',
            cls.TXT_DIR,
            cls.PDF_DIR,
            cls.EXPORTS_DIR,
            cls.RESULTS_DIR,
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    @classmethod
    def print_config(cls):
        """打印配置信息（用于调试）"""
        print("🔧 应用配置:")
        print(f"  - 数据库路径: {cls.get_database_path()}")
        print(f"  - OpenAI API Key: {'已设置' if cls.OPENAI_API_KEY else '未设置'}")
        print(f"  - OpenAI Base URL: {cls.OPENAI_BASE_URL}")
        print(f"  - OpenAI Model: {cls.OPENAI_MODEL}")
        print(f"  - TXT目录: {cls.TXT_DIR}")
        print(f"  - PDF目录: {cls.PDF_DIR}")
        print(f"  - 导出目录: {cls.EXPORTS_DIR}")


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False


class DockerConfig(Config):
    """Docker环境配置"""
    DEBUG = False
    DATABASE_PATH = 'data/database/cninfo_reports.db'


def get_config() -> Config:
    """根据环境获取配置"""
    env = os.getenv('FLASK_ENV', 'development')
    
    if env == 'production':
        return ProductionConfig()
    elif os.path.exists('/.dockerenv'):
        return DockerConfig()
    else:
        return DevelopmentConfig()
