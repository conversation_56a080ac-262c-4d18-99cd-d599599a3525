"""
分析相关路由
"""
from flask import Blueprint, request, jsonify
from datetime import datetime
from services import SpiderService, AnalysisService
from models import TaskManager
from utils.response import success_response, error_response
from utils.validation import validate_required_fields

analysis_bp = Blueprint('analysis', __name__)

# 全局变量，在app.py中初始化
spider_service = None
analysis_service = None
task_manager = None


def init_analysis_routes(spider_svc, analysis_svc, task_mgr):
    """初始化路由依赖"""
    global spider_service, analysis_service, task_manager
    spider_service = spider_svc
    analysis_service = analysis_svc
    task_manager = task_mgr


@analysis_bp.route('/start_analysis', methods=['POST'])
def start_analysis():
    """开始分析任务"""
    try:
        data = request.json
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)
        
        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        search_keyword = data.get('search_keyword', '年度报告')
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2025-12-31')
        use_online = data.get('use_online', True)
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]
        
        if not stock_codes:
            return error_response('请输入股票代码')
        
        if not keywords:
            return error_response('请输入关键词')
        
        # 创建任务
        task_name = f"关键词分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        task_id = task_manager.create_task(
            task_name=task_name,
            keywords=keywords,
            stock_codes=stock_codes
        )
        
        # 定义进度回调
        def progress_callback(current, total, message):
            task_manager.update_progress(task_id, current, total, message)
        
        # 定义任务函数
        def run_analysis_task(task_id):
            try:
                results = spider_service.crawl_and_analyze(
                    stock_codes=stock_codes,
                    keywords=keywords,
                    search_keyword=search_keyword,
                    start_date=start_date,
                    end_date=end_date,
                    use_online=use_online,
                    task_id=task_id,
                    related_parties=related_parties,
                    innovation_keywords=keywords,
                    progress_callback=progress_callback
                )
                return results
            except Exception as e:
                raise e
        
        # 启动异步任务
        task_manager.run_task_async(task_id, run_analysis_task)
        
        return success_response({
            'task_id': task_id,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return error_response(f'启动任务失败: {str(e)}')


@analysis_bp.route('/keyword_analysis', methods=['POST'])
def keyword_analysis_only():
    """仅进行关键词分析（使用本地数据）"""
    try:
        data = request.json
        
        # 验证必需字段
        required_fields = ['stock_codes', 'keywords']
        validation_error = validate_required_fields(data, required_fields)
        if validation_error:
            return error_response(validation_error)
        
        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]
        
        if not stock_codes:
            return error_response('请输入股票代码')
        
        if not keywords:
            return error_response('请输入关键词')
        
        # 执行分析
        result = analysis_service.keyword_analysis_only(stock_codes, keywords, related_parties)
        
        return jsonify(result)
        
    except Exception as e:
        return error_response(f'分析失败: {str(e)}')


@analysis_bp.route('/task_status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    try:
        task_info = task_manager.get_task(task_id)
        if task_info:
            return success_response(task_info)
        else:
            return error_response('任务不存在')
    except Exception as e:
        return error_response(f'获取任务状态失败: {str(e)}')


@analysis_bp.route('/stop_task/<task_id>', methods=['POST'])
def stop_task(task_id):
    """停止任务"""
    try:
        spider_service.stop_crawling()
        task_manager.stop_task(task_id)
        
        return success_response({'message': '任务已停止'})
    except Exception as e:
        return error_response(f'停止任务失败: {str(e)}')


@analysis_bp.route('/analysis_results/<task_id>')
def get_analysis_results(task_id):
    """获取分析结果"""
    try:
        # 先从任务管理器获取
        task_info = task_manager.get_task(task_id)
        if task_info and task_info.get('results'):
            results = task_info['results']
            # 提取analysis_results部分
            if 'analysis_results' in results:
                return success_response(results['analysis_results'])
            else:
                return success_response(results)
        else:
            # 从数据库获取结果
            analysis_results = analysis_service.db.get_keyword_analysis(task_id)
            return success_response(analysis_results)
    except Exception as e:
        return error_response(f'获取结果失败: {str(e)}')


@analysis_bp.route('/keyword_context/<analysis_id>/<keyword>', methods=['GET'])
def get_keyword_context(analysis_id, keyword):
    """获取关键词上下文片段"""
    try:
        # 获取参数
        context_length = request.args.get('context_length', '100')
        try:
            context_length = int(context_length)
        except:
            context_length = 100
        
        stock_code_filter = request.args.get('stock_code')
        file_name_filter = request.args.get('file_name')
        
        # 获取上下文
        result = analysis_service.get_keyword_context(
            analysis_id, keyword, context_length, 
            stock_code_filter, file_name_filter
        )
        
        return jsonify(result)
        
    except Exception as e:
        return error_response(f'获取上下文失败: {str(e)}')
