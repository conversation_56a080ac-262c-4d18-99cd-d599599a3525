version: '3.8'

services:
  cninfo-app:
    build: .
    ports:
      - "5000:5000"
      - "3000:3000"
    volumes:
      - ./data:/app/data
      - ./txt:/app/txt
      - ./pdf:/app/pdf
      - ./exports:/app/exports
    environment:
      - FLASK_ENV=production
      - DATABASE_PATH=data/database/cninfo_reports.db
      - TXT_DIR=txt
      - PDF_DIR=pdf
      - EXPORTS_DIR=exports
      # OpenAI配置（可选）
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - OPENAI_BASE_URL=${OPENAI_BASE_URL:-https://api.openai.com/v1}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-3.5-turbo}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/debug_database"]
      interval: 30s
      timeout: 10s
      retries: 3
