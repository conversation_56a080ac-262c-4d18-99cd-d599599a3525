'use client'

import { useState } from 'react'
import { AnalysisForm } from '@/components/AnalysisForm'
import { ResultsDisplay } from '@/components/ResultsDisplay'
import { TaskProgress } from '@/components/TaskProgress'
import { Header } from '@/components/Header'
import { Tabs } from '@/components/Tabs'

export default function Home() {
  const [activeTab, setActiveTab] = useState('analysis')
  const [currentTask, setCurrentTask] = useState<string | null>(null)
  const [analysisResults, setAnalysisResults] = useState<any>(null)

  const tabs = [
    { id: 'analysis', label: '年报分析', icon: '📊' },
    { id: 'keyword', label: '关键词分析', icon: '🔍' },
    { id: 'ai', label: 'AI分析', icon: '🤖' },
    { id: 'import', label: '数据导入', icon: '📁' },
  ]

  const handleTaskStart = (taskId: string) => {
    setCurrentTask(taskId)
    setAnalysisResults(null)
  }

  const handleTaskComplete = (results: any) => {
    setCurrentTask(null)
    setAnalysisResults(results)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          <Tabs 
            tabs={tabs} 
            activeTab={activeTab} 
            onTabChange={setActiveTab} 
          />
          
          <div className="mt-8">
            {currentTask && (
              <div className="mb-6">
                <TaskProgress 
                  taskId={currentTask} 
                  onComplete={handleTaskComplete}
                />
              </div>
            )}
            
            {activeTab === 'analysis' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <AnalysisForm 
                    mode="online"
                    onTaskStart={handleTaskStart}
                  />
                </div>
                <div>
                  {analysisResults && (
                    <ResultsDisplay results={analysisResults} />
                  )}
                </div>
              </div>
            )}
            
            {activeTab === 'keyword' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <AnalysisForm 
                    mode="local"
                    onTaskStart={handleTaskStart}
                  />
                </div>
                <div>
                  {analysisResults && (
                    <ResultsDisplay results={analysisResults} />
                  )}
                </div>
              </div>
            )}
            
            {activeTab === 'ai' && (
              <div className="space-y-6">
                <div className="card">
                  <h2 className="text-xl font-semibold mb-4">AI智能分析</h2>
                  <p className="text-gray-600">
                    使用AI技术对年报内容进行深度分析，提供智能洞察和总结。
                  </p>
                </div>
              </div>
            )}
            
            {activeTab === 'import' && (
              <div className="space-y-6">
                <div className="card">
                  <h2 className="text-xl font-semibold mb-4">数据导入</h2>
                  <p className="text-gray-600">
                    导入本地TXT文件到数据库，支持批量处理和重复检测。
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  )
}
