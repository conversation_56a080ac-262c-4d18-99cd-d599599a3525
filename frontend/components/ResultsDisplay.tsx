'use client'

import { useState } from 'react'
import { Download, Filter, Eye, ChevronDown, ChevronUp } from 'lucide-react'
import { clsx } from 'clsx'

interface ResultsDisplayProps {
  results: any[]
}

export function ResultsDisplay({ results }: ResultsDisplayProps) {
  const [filters, setFilters] = useState({
    keyword: '',
    company: '',
    minCount: 0,
    hideZero: false,
  })
  const [sortBy, setSortBy] = useState<'count' | 'company' | 'keyword'>('count')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize] = useState(20)
  const [showFilters, setShowFilters] = useState(false)

  // 过滤和排序数据
  const filteredResults = results
    .filter(item => {
      if (filters.keyword && !item.keyword.toLowerCase().includes(filters.keyword.toLowerCase())) {
        return false
      }
      if (filters.company && !item.company_name.toLowerCase().includes(filters.company.toLowerCase())) {
        return false
      }
      if (filters.minCount > 0 && item.count < filters.minCount) {
        return false
      }
      if (filters.hideZero && item.count === 0) {
        return false
      }
      return true
    })
    .sort((a, b) => {
      let aVal, bVal
      switch (sortBy) {
        case 'count':
          aVal = a.count
          bVal = b.count
          break
        case 'company':
          aVal = a.company_name
          bVal = b.company_name
          break
        case 'keyword':
          aVal = a.keyword
          bVal = b.keyword
          break
        default:
          return 0
      }
      
      if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase()
        bVal = bVal.toLowerCase()
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1
      } else {
        return aVal < bVal ? 1 : -1
      }
    })

  // 分页
  const totalPages = Math.ceil(filteredResults.length / pageSize)
  const paginatedResults = filteredResults.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  )

  const handleSort = (field: 'count' | 'company' | 'keyword') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  const handleExport = () => {
    // 导出功能实现
    console.log('导出数据:', filteredResults)
  }

  const SortIcon = ({ field }: { field: string }) => {
    if (sortBy !== field) return null
    return sortOrder === 'asc' ? 
      <ChevronUp className="w-4 h-4" /> : 
      <ChevronDown className="w-4 h-4" />
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold">分析结果</h2>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-secondary flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>筛选</span>
          </button>
          <button
            onClick={handleExport}
            className="btn-primary flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>导出</span>
          </button>
        </div>
      </div>

      {showFilters && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                关键词
              </label>
              <input
                type="text"
                className="input-field"
                placeholder="搜索关键词"
                value={filters.keyword}
                onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                公司名称
              </label>
              <input
                type="text"
                className="input-field"
                placeholder="搜索公司"
                value={filters.company}
                onChange={(e) => setFilters({ ...filters, company: e.target.value })}
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                最小出现次数
              </label>
              <input
                type="number"
                className="input-field"
                min="0"
                value={filters.minCount}
                onChange={(e) => setFilters({ ...filters, minCount: parseInt(e.target.value) || 0 })}
              />
            </div>
            <div className="flex items-end">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={filters.hideZero}
                  onChange={(e) => setFilters({ ...filters, hideZero: e.target.checked })}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="text-sm text-gray-700">隐藏零次记录</span>
              </label>
            </div>
          </div>
        </div>
      )}

      <div className="text-sm text-gray-600 mb-4">
        共 {filteredResults.length} 条结果
        {filteredResults.length !== results.length && (
          <span className="ml-2">（已筛选，原始数据 {results.length} 条）</span>
        )}
      </div>

      <div className="table-container">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                className="table-header cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('company')}
              >
                <div className="flex items-center space-x-1">
                  <span>公司名称</span>
                  <SortIcon field="company" />
                </div>
              </th>
              <th
                className="table-header cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('keyword')}
              >
                <div className="flex items-center space-x-1">
                  <span>关键词</span>
                  <SortIcon field="keyword" />
                </div>
              </th>
              <th
                className="table-header cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('count')}
              >
                <div className="flex items-center space-x-1">
                  <span>出现次数</span>
                  <SortIcon field="count" />
                </div>
              </th>
              <th className="table-header">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedResults.map((item, index) => (
              <tr key={index} className="hover:bg-gray-50">
                <td className="table-cell font-medium">
                  {item.company_name}
                </td>
                <td className="table-cell">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    {item.keyword}
                  </span>
                </td>
                <td className="table-cell">
                  <span className={clsx(
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    item.count > 0 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  )}>
                    {item.count}
                  </span>
                </td>
                <td className="table-cell">
                  {item.count > 0 && (
                    <button className="text-primary-600 hover:text-primary-900 flex items-center space-x-1">
                      <Eye className="w-4 h-4" />
                      <span>查看上下文</span>
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between mt-6">
          <div className="text-sm text-gray-700">
            显示第 {(currentPage - 1) * pageSize + 1} - {Math.min(currentPage * pageSize, filteredResults.length)} 条，
            共 {filteredResults.length} 条
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span className="text-sm text-gray-700">
              第 {currentPage} / {totalPages} 页
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
