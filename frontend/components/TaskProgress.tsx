'use client'

import { useState, useEffect } from 'react'
import { CheckCircle, XCircle, Loader2, Square } from 'lucide-react'
import { api } from '@/lib/api'

interface TaskProgressProps {
  taskId: string
  onComplete: (results: any) => void
}

export function TaskProgress({ taskId, onComplete }: TaskProgressProps) {
  const [status, setStatus] = useState<any>(null)
  const [isPolling, setIsPolling] = useState(true)

  useEffect(() => {
    if (!taskId || !isPolling) return

    const pollStatus = async () => {
      try {
        const response = await api.get(`/api/task_status/${taskId}`)
        if (response.data.success) {
          const taskStatus = response.data.data
          setStatus(taskStatus)

          if (taskStatus.status === 'completed') {
            setIsPolling(false)
            // 获取分析结果
            const resultsResponse = await api.get(`/api/analysis_results/${taskId}`)
            if (resultsResponse.data.success) {
              onComplete(resultsResponse.data.data)
            }
          } else if (taskStatus.status === 'error' || taskStatus.status === 'stopped') {
            setIsPolling(false)
          }
        }
      } catch (error) {
        console.error('获取任务状态失败:', error)
      }
    }

    // 立即执行一次
    pollStatus()

    // 设置轮询
    const interval = setInterval(pollStatus, 2000)

    return () => clearInterval(interval)
  }, [taskId, isPolling, onComplete])

  const handleStop = async () => {
    try {
      await api.post(`/api/stop_task/${taskId}`)
      setIsPolling(false)
    } catch (error) {
      console.error('停止任务失败:', error)
    }
  }

  if (!status) {
    return (
      <div className="card">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-5 h-5 animate-spin text-primary-600" />
          <span>正在获取任务状态...</span>
        </div>
      </div>
    )
  }

  const getStatusIcon = () => {
    switch (status.status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'error':
      case 'stopped':
        return <XCircle className="w-5 h-5 text-red-600" />
      default:
        return <Loader2 className="w-5 h-5 animate-spin text-primary-600" />
    }
  }

  const getStatusColor = () => {
    switch (status.status) {
      case 'completed':
        return 'text-green-600'
      case 'error':
      case 'stopped':
        return 'text-red-600'
      default:
        return 'text-primary-600'
    }
  }

  return (
    <div className="card">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon()}
            <div>
              <h3 className="font-medium">任务进度</h3>
              <p className={`text-sm ${getStatusColor()}`}>
                {status.message}
              </p>
            </div>
          </div>
          
          {status.status === 'running' && (
            <button
              onClick={handleStop}
              className="btn-danger flex items-center space-x-2"
            >
              <Square className="w-4 h-4" />
              <span>停止任务</span>
            </button>
          )}
        </div>

        {status.status === 'running' && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-gray-600">
              <span>进度: {status.current_step || 0} / {status.total_steps || 0}</span>
              <span>{status.progress || 0}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${status.progress || 0}%` }}
              />
            </div>
          </div>
        )}

        {status.start_time && (
          <div className="text-xs text-gray-500">
            开始时间: {new Date(status.start_time).toLocaleString()}
            {status.end_time && (
              <span className="ml-4">
                结束时间: {new Date(status.end_time).toLocaleString()}
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
