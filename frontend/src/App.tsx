import React, { useState } from 'react'
import { Toaster } from 'react-hot-toast'

function App() {
  const [activeTab, setActiveTab] = useState('analysis')

  const tabs = [
    { id: 'analysis', label: '年报分析', icon: '📊' },
    { id: 'keyword', label: '关键词分析', icon: '🔍' },
    { id: 'ai', label: 'AI分析', icon: '🤖' },
    { id: 'import', label: '数据导入', icon: '📁' },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-lg">
                <span className="text-white text-xl">📊</span>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  巨潮资讯网年报爬虫工具
                </h1>
                <p className="text-sm text-gray-500">
                  智能年报分析与关键词统计
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-7xl mx-auto">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="text-lg">{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="mt-8">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-semibold mb-4">
                {tabs.find(tab => tab.id === activeTab)?.label}
              </h2>
              <p className="text-gray-600">
                功能开发中... 请稍后再试。
              </p>
              
              {activeTab === 'analysis' && (
                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    在线年报分析功能：从巨潮资讯网爬取年报数据并进行关键词分析
                  </p>
                </div>
              )}
              
              {activeTab === 'keyword' && (
                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    本地关键词分析：对已导入的TXT文件进行关键词统计
                  </p>
                </div>
              )}
              
              {activeTab === 'ai' && (
                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    AI智能分析：使用AI技术对年报内容进行深度分析
                  </p>
                </div>
              )}
              
              {activeTab === 'import' && (
                <div className="mt-4">
                  <p className="text-sm text-gray-500">
                    数据导入：导入本地TXT文件到数据库
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
    </div>
  )
}

export default App
