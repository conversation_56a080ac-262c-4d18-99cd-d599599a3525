@echo off
echo ========================================
echo 巨潮资讯网年报爬虫工具 - 重构版
echo ========================================
echo.

echo 正在启动后端服务器...
cd backend
start "Backend Server" cmd /k "python app.py"

echo 等待后端启动...
timeout /t 3 /nobreak >nul

echo 正在启动前端开发服务器...
cd ..\frontend
start "Frontend Server" cmd /k "npm run dev"

echo.
echo ========================================
echo 服务启动完成！
echo 后端地址: http://localhost:5000
echo 前端地址: http://localhost:3000
echo ========================================
echo.
echo 按任意键退出...
pause >nul
