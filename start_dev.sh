#!/bin/bash

echo "========================================"
echo "巨潮资讯网年报爬虫工具 - 重构版"
echo "========================================"
echo

echo "正在启动后端服务器..."
cd backend
python app.py &
BACKEND_PID=$!

echo "等待后端启动..."
sleep 3

echo "正在启动前端开发服务器..."
cd ../frontend
npm run dev &
FRONTEND_PID=$!

echo
echo "========================================"
echo "服务启动完成！"
echo "后端地址: http://localhost:5000"
echo "前端地址: http://localhost:3000"
echo "========================================"
echo
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
